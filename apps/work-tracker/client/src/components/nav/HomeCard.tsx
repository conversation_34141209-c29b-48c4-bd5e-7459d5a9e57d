import { Card, CardContent } from "@/components/ui/card";
import { User, Activity, Cpu } from "lucide-react";
import { useRouter } from "next/navigation";

type HomeCardProps = {
    title: string;
};

const renderIcon = (title: string) => {
    switch (title) {
        case "Devices":
            return <Cpu />;
        case "Jobs":
            return <Activity />;
        case "Parts":
            return <Cpu />;
        case "Employees":
            return <User />;
    }
};

const HomeCard = ({ title }: HomeCardProps) => {
    const router = useRouter();

    const navTo = (title: string) => {
        switch (title) {
            case "Devices":
                router.push("/device");
                break;
            case "Jobs":
                router.push("/jobs");
                break;
            case "Parts":
                router.push("/parts");
                break;
            case "Employees":
                router.push("/employees");
                break;
            default:
                router.push("/");
        }
    };

    return (
        <Card
            className="w-80 h-60 flex md:m-10 items-center justify-center shadow-md rounded-xl cursor-pointer"
            onClick={() => navTo(title)}
        >
            <CardContent className="flex flex-col items-center justify-center p-4 gap-2">
                {renderIcon(title)}
                <span className="text-lg font-medium text-center">{title}</span>
            </CardContent>
        </Card>
    );
};

export default HomeCard;
