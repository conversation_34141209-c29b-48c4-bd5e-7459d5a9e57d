import {
    NavigationMenu,
    NavigationMenuItem,
    NavigationMenuLink,
    NavigationMenuList,
} from "@/components/ui/navigation-menu";
import Link from "next/link";
import Logo from "./Logo";

export default function Navbar() {
    return (
        <nav className="border-border border-b bg-white shadow-sm z-50">
            <div className="max-w-[95rem] mx-auto px-4 py-3 flex items-center justify-between">
                <Link href="/" className="text-xl font-bold text-black">
                    <Logo />
                </Link>

                <NavigationMenu>
                    <NavigationMenuList>
                        <NavigationMenuItem>
                            <NavigationMenuLink asChild>
                                <Link
                                    href="/device"
                                    className="px-4 py-2 hover:underline"
                                >
                                    Devices
                                </Link>
                            </NavigationMenuLink>
                        </NavigationMenuItem>
                        <NavigationMenuItem>
                            <NavigationMenuLink asChild>
                                <Link
                                    href="/jobs"
                                    className="px-4 py-2 hover:underline"
                                >
                                    Jobs
                                </Link>
                            </NavigationMenuLink>
                        </NavigationMenuItem>
                        <NavigationMenuItem>
                            <NavigationMenuLink asChild>
                                <Link
                                    href="/parts"
                                    className="px-4 py-2 hover:underline"
                                >
                                    Parts
                                </Link>
                            </NavigationMenuLink>
                        </NavigationMenuItem>
                        <NavigationMenuItem>
                            <NavigationMenuLink asChild>
                                <Link
                                    href="/employees"
                                    className="px-4 py-2 hover:underline"
                                >
                                    Employees
                                </Link>
                            </NavigationMenuLink>
                        </NavigationMenuItem>
                    </NavigationMenuList>
                </NavigationMenu>
            </div>
        </nav>
    );
}
