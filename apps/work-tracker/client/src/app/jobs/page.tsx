"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import JobsTable from "@/features/jobs/components/table/JobsTable";
// import UploadExcelFile from "@/features/jobs/UploadExcelFile";
import useTitle from "@/hooks/useTitle";
import { useRouter } from "next/navigation";
import { useState } from "react";

function JobsPage() {
    const router = useRouter();
    const [searchValue, setSearchValue] = useState("");
    useTitle("Manage Jobs");

    return (
        <main className="w-full max-w-screen-2xl p-8 mx-auto flex flex-col">
            <div className="flex flex-col items-start justify-between pb-4 gap-12">
                <h1 className="font-semibold text-3xl ">Manage Jobs</h1>

                <div className="w-full flex flex-row items-center gap-3 flex-1">
                    <Button onClick={() => router.push("/jobs/new")}>
                        Add New Job
                    </Button>
                    {/* <UploadExcelFile /> */}

                    <Input
                        type="text"
                        placeholder="Search jobs..."
                        value={searchValue}
                        onChange={(event) => setSearchValue(event.target.value)}
                    />
                </div>
            </div>
            <div className="flex justify-center items-center gap-4">
                <JobsTable searchValue={searchValue} />
            </div>
        </main>
    );
}

export default JobsPage;
