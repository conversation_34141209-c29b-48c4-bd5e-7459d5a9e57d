"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import PartsTable from "@/features/parts/components/table/PartsTable";
import useTitle from "@/hooks/useTitle";
import { useRouter } from "next/navigation";
import { useState } from "react";

function PartsPage() {
    const router = useRouter();
    const [searchValue, setSearchValue] = useState("");
    useTitle("Manage Parts");

    return (
        <main className="w-full max-w-screen-2xl p-8 mx-auto flex flex-col">
            <div className="w-full flex flex-col items-start pb-4 gap-y-12">
                <h1 className="font-semibold text-3xl">Manage Parts</h1>

                <div className="w-full flex flex-row items-center gap-3 flex-1">
                    <Button onClick={() => router.push("/parts/new")}>
                        Add New Part
                    </Button>
                    <Input
                        type="text"
                        placeholder="Search parts..."
                        value={searchValue}
                        onChange={(event) => setSearchValue(event.target.value)}
                    />
                </div>
            </div>
            <div className="flex justify-center items-center gap-4">
                <PartsTable searchValue={searchValue} />
            </div>
        </main>
    );
}

export default PartsPage;
