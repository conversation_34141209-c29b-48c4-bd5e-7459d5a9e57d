"use client";

import React, { useEffect } from "react";
import { useForm, useFieldArray } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { Loader2 } from "lucide-react";

import { Form } from "@/components/ui/form";
import { Button } from "@/components/ui/button";

import {
    jobFormSchema,
    type JobFormData,
} from "@/features/jobs/lib/validations";
import { devices, parts } from "@/zero/generatedSchema";
import { JobInfoSection } from "./JobInfoSection";
import { JobConfigSection } from "./JobConfigSection";

interface JobsFormProps {
    initialValues?: JobFormData;
    availableParts: parts[];
    availableDevices: devices[];
    onSubmit: (data: JobFormData) => Promise<void>;
    isSubmitting?: boolean;
    onCancel?: () => void;
}

export default function JobsForm({
    initialValues,
    availableParts,
    availableDevices,
    onSubmit,
    isSubmitting = false,
    onCancel,
}: JobsFormProps) {
    // Create form with default values
    const form = useForm<JobFormData>({
        resolver: zodResolver(jobFormSchema),
        defaultValues: initialValues || {
            partId: undefined,
            name: "",
            displayName: "",
            rework: false,
            isActive: true,
            configs: undefined,
        },
        mode: "onChange",
    });

    // Setup field array for configs
    const { fields, append, remove } = useFieldArray({
        control: form.control,
        name: "configs",
    });

    // Reset form when initialValues change
    useEffect(() => {
        if (initialValues) {
            form.reset(initialValues);
        }
    }, [initialValues, form]);

    const addConfiguration = () => {
        append({
            deviceType: "",
            deviceId: 0,
            goalCount: 0,
            expectedDuration: 0,
        });
    };

    const handleSubmit = async (data: JobFormData) => {
        try {
            await onSubmit(data);
        } catch (error) {
            console.error("Form submission error:", error);
        }
    };

    return (
        <Form {...form}>
            <form
                onSubmit={form.handleSubmit(handleSubmit)}
                className="space-y-8"
            >
                <JobInfoSection
                    control={form.control}
                    availableParts={availableParts}
                />
                <JobConfigSection
                    control={form.control}
                    availableDevices={availableDevices}
                    addConfiguration={addConfiguration}
                    fields={fields}
                    remove={remove}
                />
                <div className="flex items-center justify-end space-x-4 pt-6">
                    {onCancel && (
                        <Button
                            type="button"
                            variant="outline"
                            onClick={onCancel}
                        >
                            Cancel
                        </Button>
                    )}
                    <Button type="submit" disabled={isSubmitting}>
                        {isSubmitting && (
                            <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                        )}
                        {initialValues ? "Update Job" : "Create Job"}
                    </Button>
                </div>
            </form>
        </Form>
    );
}
